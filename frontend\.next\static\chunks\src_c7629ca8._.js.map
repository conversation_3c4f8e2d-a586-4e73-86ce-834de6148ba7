{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Desktop/voice_buddy/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\nexport interface ChatMessage {\n  id: number;\n  content: string;\n  is_user_message: boolean;\n  message_type: string;\n  voice_file_path?: string;\n  created_at: string;\n}\n\nexport interface Conversation {\n  id: number;\n  title?: string;\n  language: string;\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface User {\n  id: number;\n  username: string;\n  email?: string;\n  preferred_language: string;\n  voice_enabled: boolean;\n  created_at: string;\n}\n\nexport interface ChatRequest {\n  message: string;\n  conversation_id?: number;\n  username: string;\n  language: string;\n  response_type: 'text' | 'voice' | 'both';\n}\n\nexport interface ChatResponse {\n  message: string;\n  conversation_id: number;\n  response_text: string;\n  voice_file_url?: string;\n}\n\n// Chat API\nexport const chatAPI = {\n  sendMessage: async (request: ChatRequest): Promise<ChatResponse> => {\n    const response = await api.post('/chat/send', request);\n    return response.data;\n  },\n\n  getConversations: async (userId: number): Promise<Conversation[]> => {\n    const response = await api.get(`/chat/conversations/${userId}`);\n    return response.data;\n  },\n\n  getMessages: async (conversationId: number): Promise<ChatMessage[]> => {\n    const response = await api.get(`/chat/conversations/${conversationId}/messages`);\n    return response.data;\n  },\n\n  createConversation: async (username: string, language: string = 'en') => {\n    const response = await api.post('/chat/conversations/new', null, {\n      params: { username, language }\n    });\n    return response.data;\n  }\n};\n\n// Voice API\nexport const voiceAPI = {\n  speechToText: async (audioFile: File, language: string = 'en'): Promise<{text: string, success: boolean}> => {\n    const formData = new FormData();\n    formData.append('audio_file', audioFile);\n    \n    const response = await api.post('/voice/speech-to-text', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      params: { language }\n    });\n    return response.data;\n  },\n\n  textToSpeech: async (text: string, language: string = 'en'): Promise<{voice_file_url: string, success: boolean}> => {\n    const response = await api.post('/voice/text-to-speech', { text, language });\n    return response.data;\n  },\n\n  voiceChat: async (audioFile: File, username: string, conversationId?: number, language: string = 'en', responseType: string = 'both') => {\n    const formData = new FormData();\n    formData.append('audio_file', audioFile);\n    \n    const response = await api.post('/voice/voice-chat', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n      params: {\n        username,\n        conversation_id: conversationId,\n        language,\n        response_type: responseType\n      }\n    });\n    return response.data;\n  }\n};\n\n// User API\nexport const userAPI = {\n  getUser: async (username: string): Promise<User> => {\n    const response = await api.get(`/users/${username}`);\n    return response.data;\n  },\n\n  updateUser: async (username: string, updates: Partial<User>): Promise<User> => {\n    const response = await api.put(`/users/${username}`, updates);\n    return response.data;\n  },\n\n  getSupportedLanguages: async (): Promise<{languages: string[], default: string}> => {\n    const response = await api.get('/users/languages/supported');\n    return response.data;\n  }\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;AAEqB;AAFrB;;AAEA,MAAM,eAAe,oEAAmC;AAExD,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AA4CO,MAAM,UAAU;IACrB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,uBAA6B,OAAP;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,uBAAqC,OAAf,gBAAe;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB,eAAO;YAAkB,4EAAmB;QAC9D,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B,MAAM;YAC/D,QAAQ;gBAAE;gBAAU;YAAS;QAC/B;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,WAAW;IACtB,cAAc,eAAO;YAAiB,4EAAmB;QACvD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,cAAc;QAE9B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;YACA,QAAQ;gBAAE;YAAS;QACrB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc,eAAO;YAAc,4EAAmB;QACpD,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,yBAAyB;YAAE;YAAM;QAAS;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW,eAAO,WAAiB,UAAkB;YAAyB,4EAAmB,MAAM,gFAAuB;QAC5H,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,cAAc;QAE9B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB,UAAU;YAC7D,SAAS;gBACP,gBAAgB;YAClB;YACA,QAAQ;gBACN;gBACA,iBAAiB;gBACjB;gBACA,eAAe;YACjB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAkB,OAAT;QACzC,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO,UAAkB;QACnC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,UAAkB,OAAT,WAAY;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Desktop/voice_buddy/frontend/src/hooks/useVoiceRecording.ts"], "sourcesContent": ["import { useState, useRef, useCallback } from 'react';\n\nexport interface UseVoiceRecordingReturn {\n  isRecording: boolean;\n  startRecording: () => Promise<void>;\n  stopRecording: () => Promise<Blob | null>;\n  audioBlob: Blob | null;\n  error: string | null;\n}\n\nexport const useVoiceRecording = (): UseVoiceRecordingReturn => {\n  const [isRecording, setIsRecording] = useState(false);\n  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  \n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\n  const audioChunksRef = useRef<Blob[]>([]);\n\n  const startRecording = useCallback(async () => {\n    try {\n      setError(null);\n      \n      const stream = await navigator.mediaDevices.getUserMedia({ \n        audio: {\n          echoCancellation: true,\n          noiseSuppression: true,\n          sampleRate: 44100,\n        } \n      });\n      \n      const mediaRecorder = new MediaRecorder(stream, {\n        mimeType: 'audio/webm;codecs=opus'\n      });\n      \n      audioChunksRef.current = [];\n      \n      mediaRecorder.ondataavailable = (event) => {\n        if (event.data.size > 0) {\n          audioChunksRef.current.push(event.data);\n        }\n      };\n      \n      mediaRecorder.onstop = () => {\n        const audioBlob = new Blob(audioChunksRef.current, { \n          type: 'audio/webm;codecs=opus' \n        });\n        setAudioBlob(audioBlob);\n        \n        // Stop all tracks to release microphone\n        stream.getTracks().forEach(track => track.stop());\n      };\n      \n      mediaRecorderRef.current = mediaRecorder;\n      mediaRecorder.start();\n      setIsRecording(true);\n      \n    } catch (err) {\n      console.error('Error starting recording:', err);\n      setError('Failed to start recording. Please check microphone permissions.');\n    }\n  }, []);\n\n  const stopRecording = useCallback(async (): Promise<Blob | null> => {\n    return new Promise((resolve) => {\n      if (mediaRecorderRef.current && isRecording) {\n        mediaRecorderRef.current.onstop = () => {\n          const audioBlob = new Blob(audioChunksRef.current, { \n            type: 'audio/webm;codecs=opus' \n          });\n          setAudioBlob(audioBlob);\n          setIsRecording(false);\n          resolve(audioBlob);\n        };\n        \n        mediaRecorderRef.current.stop();\n      } else {\n        resolve(null);\n      }\n    });\n  }, [isRecording]);\n\n  return {\n    isRecording,\n    startRecording,\n    stopRecording,\n    audioBlob,\n    error\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;;AAUO,MAAM,oBAAoB;;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IAExC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACjC,IAAI;gBACF,SAAS;gBAET,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;oBACvD,OAAO;wBACL,kBAAkB;wBAClB,kBAAkB;wBAClB,YAAY;oBACd;gBACF;gBAEA,MAAM,gBAAgB,IAAI,cAAc,QAAQ;oBAC9C,UAAU;gBACZ;gBAEA,eAAe,OAAO,GAAG,EAAE;gBAE3B,cAAc,eAAe;qEAAG,CAAC;wBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;4BACvB,eAAe,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;wBACxC;oBACF;;gBAEA,cAAc,MAAM;qEAAG;wBACrB,MAAM,YAAY,IAAI,KAAK,eAAe,OAAO,EAAE;4BACjD,MAAM;wBACR;wBACA,aAAa;wBAEb,wCAAwC;wBACxC,OAAO,SAAS,GAAG,OAAO;6EAAC,CAAA,QAAS,MAAM,IAAI;;oBAChD;;gBAEA,iBAAiB,OAAO,GAAG;gBAC3B,cAAc,KAAK;gBACnB,eAAe;YAEjB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;YACX;QACF;wDAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;YAChC,OAAO,IAAI;gEAAQ,CAAC;oBAClB,IAAI,iBAAiB,OAAO,IAAI,aAAa;wBAC3C,iBAAiB,OAAO,CAAC,MAAM;4EAAG;gCAChC,MAAM,YAAY,IAAI,KAAK,eAAe,OAAO,EAAE;oCACjD,MAAM;gCACR;gCACA,aAAa;gCACb,eAAe;gCACf,QAAQ;4BACV;;wBAEA,iBAAiB,OAAO,CAAC,IAAI;oBAC/B,OAAO;wBACL,QAAQ;oBACV;gBACF;;QACF;uDAAG;QAAC;KAAY;IAEhB,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GA9Ea", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Desktop/voice_buddy/frontend/src/components/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Send, Mic, MicOff, Volume2, VolumeX, Settings, MessageSquare } from 'lucide-react';\nimport { chatAPI, voiceAPI, ChatMessage, Conversation } from '@/services/api';\nimport { useVoiceRecording } from '@/hooks/useVoiceRecording';\n\ninterface ChatInterfaceProps {\n  username: string;\n  initialLanguage?: string;\n}\n\nexport const ChatInterface: React.FC<ChatInterfaceProps> = ({ \n  username, \n  initialLanguage = 'en' \n}) => {\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\n  const [inputText, setInputText] = useState('');\n  const [currentConversation, setCurrentConversation] = useState<number | null>(null);\n  const [conversations, setConversations] = useState<Conversation[]>([]);\n  const [language, setLanguage] = useState(initialLanguage);\n  const [responseType, setResponseType] = useState<'text' | 'voice' | 'both'>('text');\n  const [isLoading, setIsLoading] = useState(false);\n  const [showSettings, setShowSettings] = useState(false);\n  \n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const audioRef = useRef<HTMLAudioElement>(null);\n  \n  const { \n    isRecording, \n    startRecording, \n    stopRecording, \n    error: recordingError \n  } = useVoiceRecording();\n\n  const supportedLanguages = [\n    { code: 'en', name: 'English' },\n    { code: 'es', name: 'Spanish' },\n    { code: 'fr', name: 'French' },\n    { code: 'de', name: 'German' },\n    { code: 'it', name: 'Italian' },\n    { code: 'pt', name: 'Portuguese' },\n    { code: 'ru', name: 'Russian' },\n    { code: 'ja', name: 'Japanese' },\n    { code: 'ko', name: 'Korean' },\n    { code: 'zh', name: 'Chinese' },\n    { code: 'ar', name: 'Arabic' },\n    { code: 'hi', name: 'Hindi' },\n  ];\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    loadConversations();\n  }, []);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const loadConversations = async () => {\n    try {\n      // For demo purposes, we'll use user ID 1\n      // In a real app, you'd get this from authentication\n      const convs = await chatAPI.getConversations(1);\n      setConversations(convs);\n    } catch (error) {\n      console.error('Error loading conversations:', error);\n    }\n  };\n\n  const loadMessages = async (conversationId: number) => {\n    try {\n      const msgs = await chatAPI.getMessages(conversationId);\n      setMessages(msgs);\n      setCurrentConversation(conversationId);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    }\n  };\n\n  const sendTextMessage = async () => {\n    if (!inputText.trim()) return;\n\n    setIsLoading(true);\n    try {\n      const response = await chatAPI.sendMessage({\n        message: inputText,\n        conversation_id: currentConversation || undefined,\n        username,\n        language,\n        response_type: responseType\n      });\n\n      // Add user message\n      const userMessage: ChatMessage = {\n        id: Date.now(),\n        content: inputText,\n        is_user_message: true,\n        message_type: 'text',\n        created_at: new Date().toISOString()\n      };\n\n      // Add AI response\n      const aiMessage: ChatMessage = {\n        id: Date.now() + 1,\n        content: response.response_text,\n        is_user_message: false,\n        message_type: responseType,\n        voice_file_path: response.voice_file_url,\n        created_at: new Date().toISOString()\n      };\n\n      setMessages(prev => [...prev, userMessage, aiMessage]);\n      setCurrentConversation(response.conversation_id);\n      setInputText('');\n\n      // Play voice response if available\n      if (response.voice_file_url && (responseType === 'voice' || responseType === 'both')) {\n        playAudio(response.voice_file_url);\n      }\n\n      // Reload conversations to update the list\n      loadConversations();\n\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const sendVoiceMessage = async () => {\n    if (isRecording) {\n      const audioBlob = await stopRecording();\n      if (audioBlob) {\n        setIsLoading(true);\n        try {\n          const audioFile = new File([audioBlob], 'recording.webm', { \n            type: 'audio/webm' \n          });\n\n          const response = await voiceAPI.voiceChat(\n            audioFile,\n            username,\n            currentConversation || undefined,\n            language,\n            responseType\n          );\n\n          // Add user message\n          const userMessage: ChatMessage = {\n            id: Date.now(),\n            content: response.user_text,\n            is_user_message: true,\n            message_type: 'voice',\n            created_at: new Date().toISOString()\n          };\n\n          // Add AI response\n          const aiMessage: ChatMessage = {\n            id: Date.now() + 1,\n            content: response.ai_response,\n            is_user_message: false,\n            message_type: responseType,\n            voice_file_path: response.voice_file_url,\n            created_at: new Date().toISOString()\n          };\n\n          setMessages(prev => [...prev, userMessage, aiMessage]);\n          setCurrentConversation(response.conversation_id);\n\n          // Play voice response if available\n          if (response.voice_file_url && (responseType === 'voice' || responseType === 'both')) {\n            playAudio(response.voice_file_url);\n          }\n\n          loadConversations();\n\n        } catch (error) {\n          console.error('Error sending voice message:', error);\n        } finally {\n          setIsLoading(false);\n        }\n      }\n    } else {\n      startRecording();\n    }\n  };\n\n  const playAudio = (audioUrl: string) => {\n    if (audioRef.current) {\n      audioRef.current.src = `http://localhost:8000${audioUrl}`;\n      audioRef.current.play().catch(console.error);\n    }\n  };\n\n  const startNewConversation = async () => {\n    try {\n      await chatAPI.createConversation(username, language);\n      setMessages([]);\n      setCurrentConversation(null);\n      loadConversations();\n    } catch (error) {\n      console.error('Error creating conversation:', error);\n    }\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100\">\n      {/* Sidebar */}\n      <div className=\"w-80 bg-white border-r border-gray-200 flex flex-col\">\n        <div className=\"p-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-xl font-semibold\">Voice Buddy</h2>\n            <button\n              onClick={() => setShowSettings(!showSettings)}\n              className=\"p-2 hover:bg-gray-100 rounded-lg\"\n            >\n              <Settings size={20} />\n            </button>\n          </div>\n          \n          <button\n            onClick={startNewConversation}\n            className=\"w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center gap-2\"\n          >\n            <MessageSquare size={16} />\n            New Chat\n          </button>\n        </div>\n\n        {/* Settings Panel */}\n        {showSettings && (\n          <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Language</label>\n                <select\n                  value={language}\n                  onChange={(e) => setLanguage(e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 rounded-lg\"\n                >\n                  {supportedLanguages.map(lang => (\n                    <option key={lang.code} value={lang.code}>\n                      {lang.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium mb-2\">Response Type</label>\n                <select\n                  value={responseType}\n                  onChange={(e) => setResponseType(e.target.value as 'text' | 'voice' | 'both')}\n                  className=\"w-full p-2 border border-gray-300 rounded-lg\"\n                >\n                  <option value=\"text\">Text Only</option>\n                  <option value=\"voice\">Voice Only</option>\n                  <option value=\"both\">Text + Voice</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Conversations List */}\n        <div className=\"flex-1 overflow-y-auto\">\n          {conversations.map(conv => (\n            <div\n              key={conv.id}\n              onClick={() => loadMessages(conv.id)}\n              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${\n                currentConversation === conv.id ? 'bg-blue-50 border-blue-200' : ''\n              }`}\n            >\n              <div className=\"font-medium truncate\">\n                {conv.title || 'New Conversation'}\n              </div>\n              <div className=\"text-sm text-gray-500\">\n                {new Date(conv.created_at).toLocaleDateString()}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Main Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {messages.map(message => (\n            <div\n              key={message.id}\n              className={`flex ${message.is_user_message ? 'justify-end' : 'justify-start'}`}\n            >\n              <div\n                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                  message.is_user_message\n                    ? 'bg-blue-500 text-white'\n                    : 'bg-white border border-gray-200'\n                }`}\n              >\n                <div className=\"text-sm\">{message.content}</div>\n                {message.voice_file_path && (\n                  <button\n                    onClick={() => playAudio(message.voice_file_path!)}\n                    className=\"mt-2 p-1 hover:bg-gray-100 rounded\"\n                  >\n                    <Volume2 size={16} />\n                  </button>\n                )}\n              </div>\n            </div>\n          ))}\n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-white border border-gray-200 px-4 py-2 rounded-lg\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"></div>\n                  <span className=\"text-sm\">AI is thinking...</span>\n                </div>\n              </div>\n            </div>\n          )}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Input Area */}\n        <div className=\"border-t border-gray-200 p-4\">\n          {recordingError && (\n            <div className=\"mb-2 text-red-500 text-sm\">{recordingError}</div>\n          )}\n          \n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"text\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && sendTextMessage()}\n              placeholder=\"Type your message...\"\n              className=\"flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              disabled={isLoading}\n            />\n            \n            <button\n              onClick={sendTextMessage}\n              disabled={isLoading || !inputText.trim()}\n              className=\"p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <Send size={20} />\n            </button>\n            \n            <button\n              onClick={sendVoiceMessage}\n              disabled={isLoading}\n              className={`p-3 rounded-lg ${\n                isRecording \n                  ? 'bg-red-500 text-white animate-pulse' \n                  : 'bg-green-500 text-white hover:bg-green-600'\n              } disabled:opacity-50 disabled:cursor-not-allowed`}\n            >\n              {isRecording ? <MicOff size={20} /> : <Mic size={20} />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Hidden audio element for playing responses */}\n      <audio ref={audioRef} />\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAYO,MAAM,gBAA8C;QAAC,EAC1D,QAAQ,EACR,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,EACJ,WAAW,EACX,cAAc,EACd,aAAa,EACb,OAAO,cAAc,EACtB,GAAG,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,qBAAqB;QACzB;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAS;QAC7B;YAAE,MAAM;YAAM,MAAM;QAAS;QAC7B;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAa;QACjC;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAW;QAC/B;YAAE,MAAM;YAAM,MAAM;QAAS;QAC7B;YAAE,MAAM;YAAM,MAAM;QAAU;QAC9B;YAAE,MAAM;YAAM,MAAM;QAAS;QAC7B;YAAE,MAAM;YAAM,MAAM;QAAQ;KAC7B;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,yCAAyC;YACzC,oDAAoD;YACpD,MAAM,QAAQ,MAAM,yHAAA,CAAA,UAAO,CAAC,gBAAgB,CAAC;YAC7C,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,OAAO,MAAM,yHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;YACvC,YAAY;YACZ,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAO,CAAC,WAAW,CAAC;gBACzC,SAAS;gBACT,iBAAiB,uBAAuB;gBACxC;gBACA;gBACA,eAAe;YACjB;YAEA,mBAAmB;YACnB,MAAM,cAA2B;gBAC/B,IAAI,KAAK,GAAG;gBACZ,SAAS;gBACT,iBAAiB;gBACjB,cAAc;gBACd,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,kBAAkB;YAClB,MAAM,YAAyB;gBAC7B,IAAI,KAAK,GAAG,KAAK;gBACjB,SAAS,SAAS,aAAa;gBAC/B,iBAAiB;gBACjB,cAAc;gBACd,iBAAiB,SAAS,cAAc;gBACxC,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;oBAAa;iBAAU;YACrD,uBAAuB,SAAS,eAAe;YAC/C,aAAa;YAEb,mCAAmC;YACnC,IAAI,SAAS,cAAc,IAAI,CAAC,iBAAiB,WAAW,iBAAiB,MAAM,GAAG;gBACpF,UAAU,SAAS,cAAc;YACnC;YAEA,0CAA0C;YAC1C;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa;YACf,MAAM,YAAY,MAAM;YACxB,IAAI,WAAW;gBACb,aAAa;gBACb,IAAI;oBACF,MAAM,YAAY,IAAI,KAAK;wBAAC;qBAAU,EAAE,kBAAkB;wBACxD,MAAM;oBACR;oBAEA,MAAM,WAAW,MAAM,yHAAA,CAAA,WAAQ,CAAC,SAAS,CACvC,WACA,UACA,uBAAuB,WACvB,UACA;oBAGF,mBAAmB;oBACnB,MAAM,cAA2B;wBAC/B,IAAI,KAAK,GAAG;wBACZ,SAAS,SAAS,SAAS;wBAC3B,iBAAiB;wBACjB,cAAc;wBACd,YAAY,IAAI,OAAO,WAAW;oBACpC;oBAEA,kBAAkB;oBAClB,MAAM,YAAyB;wBAC7B,IAAI,KAAK,GAAG,KAAK;wBACjB,SAAS,SAAS,WAAW;wBAC7B,iBAAiB;wBACjB,cAAc;wBACd,iBAAiB,SAAS,cAAc;wBACxC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBAEA,YAAY,CAAA,OAAQ;+BAAI;4BAAM;4BAAa;yBAAU;oBACrD,uBAAuB,SAAS,eAAe;oBAE/C,mCAAmC;oBACnC,IAAI,SAAS,cAAc,IAAI,CAAC,iBAAiB,WAAW,iBAAiB,MAAM,GAAG;wBACpF,UAAU,SAAS,cAAc;oBACnC;oBAEA;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAChD,SAAU;oBACR,aAAa;gBACf;YACF;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,GAAG,GAAG,AAAC,wBAAgC,OAAT;YAC/C,SAAS,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;QAC7C;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,yHAAA,CAAA,UAAO,CAAC,kBAAkB,CAAC,UAAU;YAC3C,YAAY,EAAE;YACd,uBAAuB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCACC,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIpB,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,MAAM;;;;;;oCAAM;;;;;;;;;;;;;oBAM9B,8BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;sDAET,mBAAmB,GAAG,CAAC,CAAA,qBACtB,6LAAC;oDAAuB,OAAO,KAAK,IAAI;8DACrC,KAAK,IAAI;mDADC,KAAK,IAAI;;;;;;;;;;;;;;;;8CAO5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAiC;;;;;;sDAClD,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAO;;;;;;8DACrB,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/B,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,6LAAC;gCAEC,SAAS,IAAM,aAAa,KAAK,EAAE;gCACnC,WAAW,AAAC,gEAEX,OADC,wBAAwB,KAAK,EAAE,GAAG,+BAA+B;;kDAGnE,6LAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK,IAAI;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;kDACZ,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;+BAV1C,KAAK,EAAE;;;;;;;;;;;;;;;;0BAkBpB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAA,wBACZ,6LAAC;oCAEC,WAAW,AAAC,QAAiE,OAA1D,QAAQ,eAAe,GAAG,gBAAgB;8CAE7D,cAAA,6LAAC;wCACC,WAAW,AAAC,6CAIX,OAHC,QAAQ,eAAe,GACnB,2BACA;;0DAGN,6LAAC;gDAAI,WAAU;0DAAW,QAAQ,OAAO;;;;;;4CACxC,QAAQ,eAAe,kBACtB,6LAAC;gDACC,SAAS,IAAM,UAAU,QAAQ,eAAe;gDAChD,WAAU;0DAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;oDAAC,MAAM;;;;;;;;;;;;;;;;;mCAhBhB,QAAQ,EAAE;;;;;4BAsBlB,2BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;0CAKlC,6LAAC;gCAAI,KAAK;;;;;;;;;;;;kCAIZ,6LAAC;wBAAI,WAAU;;4BACZ,gCACC,6LAAC;gCAAI,WAAU;0CAA6B;;;;;;0CAG9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;wCACxC,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAGZ,6LAAC;wCACC,SAAS;wCACT,UAAU,aAAa,CAAC,UAAU,IAAI;wCACtC,WAAU;kDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAGd,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAW,AAAC,kBAIX,OAHC,cACI,wCACA,8CACL;kDAEA,4BAAc,6LAAC,6MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;qGAAS,6LAAC,mMAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAM,KAAK;;;;;;;;;;;;AAGlB;GA3Wa;;QAqBP,oIAAA,CAAA,oBAAiB;;;KArBV", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Desktop/voice_buddy/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { ChatInterface } from '@/components/ChatInterface';\n\nexport default function Home() {\n  return (\n    <div className=\"h-screen\">\n      <ChatInterface username=\"demo_user\" initialLanguage=\"en\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sIAAA,CAAA,gBAAa;YAAC,UAAS;YAAY,iBAAgB;;;;;;;;;;;AAG1D;KANwB", "debugId": null}}]}