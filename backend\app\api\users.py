from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Optional
from pydantic import BaseModel
from ..core.database import get_db
from ..core.config import settings
from ..services.chat_service import ChatService
from ..models.user import User

router = APIRouter()
chat_service = ChatService()

class UserCreate(BaseModel):
    username: str
    email: Optional[str] = None
    preferred_language: str = "en"

class UserResponse(BaseModel):
    id: int
    username: str
    email: Optional[str]
    preferred_language: str
    voice_enabled: bool
    created_at: str

class UserUpdate(BaseModel):
    email: Optional[str] = None
    preferred_language: Optional[str] = None
    voice_enabled: Optional[bool] = None

@router.post("/create", response_model=UserResponse)
async def create_user(user_data: UserCreate, db: Session = Depends(get_db)):
    """
    Create a new user
    """
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.username == user_data.username).first()
        if existing_user:
            raise HTTPException(status_code=400, detail="Username already exists")
        
        user = await chat_service.create_user(
            db, 
            user_data.username, 
            user_data.email, 
            user_data.preferred_language
        )
        
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            preferred_language=user.preferred_language,
            voice_enabled=user.voice_enabled,
            created_at=user.created_at.isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{username}", response_model=UserResponse)
async def get_user(username: str, db: Session = Depends(get_db)):
    """
    Get user by username
    """
    try:
        user = await chat_service.get_or_create_user(db, username)
        
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            preferred_language=user.preferred_language,
            voice_enabled=user.voice_enabled,
            created_at=user.created_at.isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{username}", response_model=UserResponse)
async def update_user(username: str, user_update: UserUpdate, db: Session = Depends(get_db)):
    """
    Update user preferences
    """
    try:
        user = await chat_service.get_or_create_user(db, username)
        
        if user_update.email is not None:
            user.email = user_update.email
        if user_update.preferred_language is not None:
            user.preferred_language = user_update.preferred_language
        if user_update.voice_enabled is not None:
            user.voice_enabled = user_update.voice_enabled
        
        db.commit()
        db.refresh(user)
        
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            preferred_language=user.preferred_language,
            voice_enabled=user.voice_enabled,
            created_at=user.created_at.isoformat()
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/languages/supported")
async def get_supported_languages():
    """
    Get list of supported languages
    """
    return {
        "languages": settings.SUPPORTED_LANGUAGES,
        "default": settings.DEFAULT_VOICE_LANGUAGE
    }
