# AI Voice Chat Bot

A multilingual AI voice chat bot with support for both text and voice communication.

## Features

- **Multilingual Support**: Chat in any language
- **Voice & Text Communication**: 
  - Voice input with speech-to-text
  - Text input
  - Voice output with text-to-speech
  - Text output
- **Real-time Chat Interface**: Modern React-based UI
- **Persistent Conversations**: PostgreSQL database storage
- **Language Selection**: Dynamic language switching

## Tech Stack

### Backend
- **Python FastAPI**: REST API server
- **PostgreSQL**: Database for conversations and user data
- **OpenAI API**: AI text generation
- **Speech Recognition**: Voice-to-text conversion
- **Text-to-Speech**: Voice response generation

### Frontend
- **Next.js**: React framework
- **React**: UI components
- **CSS/HTML**: Styling and layout
- **Web Audio API**: Voice recording and playback

## Project Structure

```
voice_buddy/
├── backend/                 # Python FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── core/           # Configuration
│   ├── requirements.txt
│   └── main.py
├── frontend/               # Next.js React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Next.js pages
│   │   ├── services/       # API calls
│   │   └── styles/         # CSS styles
│   ├── package.json
│   └── next.config.js
├── database/               # Database scripts
│   └── init.sql
└── README.md
```

## Quick Setup

Run the automated setup script:
```bash
python setup.py
```

## Manual Setup Instructions

### Prerequisites
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- OpenAI API Key

### 1. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
# Edit .env with your actual values
```

### 2. Database Setup

```bash
# Create PostgreSQL database
psql -U postgres -c "CREATE DATABASE voice_buddy;"

# Run initialization script
psql -U postgres -d voice_buddy -f database/init.sql
```

### 3. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Environment is already configured in .env.local
```

### 4. Start the Application

**Terminal 1 - Backend:**
```bash
cd backend
python main.py
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

**Access the application:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Environment Variables

### Backend (.env)
```env
DATABASE_URL=postgresql://username:password@localhost:5432/voice_buddy
OPENAI_API_KEY=your_openai_api_key_here
SECRET_KEY=your_secret_key_here
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
```

## Features Usage

### Text Chat
1. Type your message in the input field
2. Press Enter or click Send button
3. AI responds in text format

### Voice Chat
1. Click the microphone button to start recording
2. Speak your message
3. Click the microphone button again to stop recording
4. AI processes your speech and responds

### Language Selection
1. Click the Settings icon in the sidebar
2. Select your preferred language
3. Choose response type (Text, Voice, or Both)

### Multiple Conversations
1. Click "New Chat" to start a new conversation
2. Click on any conversation in the sidebar to switch
3. Each conversation maintains its own history

## API Endpoints

### Chat Endpoints
- `POST /api/v1/chat/send` - Send a message
- `GET /api/v1/chat/conversations/{user_id}` - Get user conversations
- `GET /api/v1/chat/conversations/{conversation_id}/messages` - Get messages
- `POST /api/v1/chat/conversations/new` - Create new conversation

### Voice Endpoints
- `POST /api/v1/voice/speech-to-text` - Convert speech to text
- `POST /api/v1/voice/text-to-speech` - Convert text to speech
- `POST /api/v1/voice/voice-chat` - Complete voice chat flow
- `GET /api/v1/voice/file/{filename}` - Serve voice files

### User Endpoints
- `GET /api/v1/users/{username}` - Get user info
- `PUT /api/v1/users/{username}` - Update user preferences
- `GET /api/v1/users/languages/supported` - Get supported languages
