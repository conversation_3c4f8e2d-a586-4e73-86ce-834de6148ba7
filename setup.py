#!/usr/bin/env python3
"""
Voice Buddy AI Chat Bot Setup Script
This script helps set up the development environment for the Voice Buddy project.
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, cwd=None):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error running command: {command}")
            print(f"Error output: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"Exception running command {command}: {e}")
        return False

def setup_backend():
    """Set up the backend environment."""
    print("Setting up backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("Backend directory not found!")
        return False
    
    # Create virtual environment
    print("Creating virtual environment...")
    if not run_command("python -m venv venv", cwd=backend_dir):
        return False
    
    # Install requirements
    print("Installing Python dependencies...")
    pip_command = "venv\\Scripts\\pip" if os.name == 'nt' else "venv/bin/pip"
    if not run_command(f"{pip_command} install -r requirements.txt", cwd=backend_dir):
        return False
    
    # Create .env file if it doesn't exist
    env_file = backend_dir / ".env"
    if not env_file.exists():
        print("Creating .env file...")
        with open(env_file, 'w') as f:
            f.write("DATABASE_URL=postgresql://username:password@localhost:5432/voice_buddy\n")
            f.write("OPENAI_API_KEY=your_openai_api_key_here\n")
            f.write("SECRET_KEY=your_secret_key_here\n")
        print("Please edit backend/.env with your actual configuration values.")
    
    print("Backend setup complete!")
    return True

def setup_frontend():
    """Set up the frontend environment."""
    print("Setting up frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("Frontend directory not found!")
        return False
    
    # Install npm dependencies
    print("Installing Node.js dependencies...")
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("Frontend setup complete!")
    return True

def setup_database():
    """Provide database setup instructions."""
    print("\n" + "="*50)
    print("DATABASE SETUP INSTRUCTIONS")
    print("="*50)
    print("1. Install PostgreSQL on your system")
    print("2. Create a database named 'voice_buddy':")
    print("   psql -U postgres -c \"CREATE DATABASE voice_buddy;\"")
    print("3. Run the initialization script:")
    print("   psql -U postgres -d voice_buddy -f database/init.sql")
    print("4. Update the DATABASE_URL in backend/.env with your credentials")
    print("="*50)

def main():
    """Main setup function."""
    print("Voice Buddy AI Chat Bot Setup")
    print("="*40)
    
    # Check if we're in the right directory
    if not Path("README.md").exists():
        print("Please run this script from the project root directory.")
        sys.exit(1)
    
    success = True
    
    # Setup backend
    if not setup_backend():
        success = False
    
    # Setup frontend
    if not setup_frontend():
        success = False
    
    # Database instructions
    setup_database()
    
    if success:
        print("\n" + "="*50)
        print("SETUP COMPLETE!")
        print("="*50)
        print("Next steps:")
        print("1. Configure your .env files with actual values")
        print("2. Set up PostgreSQL database (see instructions above)")
        print("3. Start the backend: cd backend && python main.py")
        print("4. Start the frontend: cd frontend && npm run dev")
        print("5. Open http://localhost:3000 in your browser")
        print("="*50)
    else:
        print("\nSetup completed with some errors. Please check the output above.")

if __name__ == "__main__":
    main()
