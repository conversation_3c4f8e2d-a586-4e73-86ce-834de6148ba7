#!/usr/bin/env python3
"""
Test script to verify Voice Buddy setup
"""

import requests
import json
import time
from pathlib import Path

def test_backend_health():
    """Test if backend is running and healthy."""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running and healthy")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend is not accessible: {e}")
        return False

def test_frontend():
    """Test if frontend is running."""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is running")
            return True
        else:
            print(f"❌ Frontend check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend is not accessible: {e}")
        return False

def test_api_endpoints():
    """Test basic API endpoints."""
    base_url = "http://localhost:8000/api/v1"
    
    # Test supported languages endpoint
    try:
        response = requests.get(f"{base_url}/users/languages/supported", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Languages API working - {len(data['languages'])} languages supported")
        else:
            print(f"❌ Languages API failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Languages API error: {e}")
        return False
    
    # Test chat endpoint with a simple message
    try:
        chat_data = {
            "message": "Hello, this is a test message",
            "username": "test_user",
            "language": "en",
            "response_type": "text"
        }
        response = requests.post(f"{base_url}/chat/send", json=chat_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Chat API working - received AI response")
        else:
            print(f"❌ Chat API failed: {response.status_code}")
            if response.text:
                print(f"Error details: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat API error: {e}")
        return False
    
    return True

def check_environment():
    """Check if environment is properly configured."""
    backend_env = Path("backend/.env")
    frontend_env = Path("frontend/.env.local")
    
    if not backend_env.exists():
        print("❌ Backend .env file not found")
        return False
    else:
        print("✅ Backend .env file exists")
    
    if not frontend_env.exists():
        print("❌ Frontend .env.local file not found")
        return False
    else:
        print("✅ Frontend .env.local file exists")
    
    # Check if OpenAI API key is configured
    try:
        with open(backend_env, 'r') as f:
            env_content = f.read()
            if "your_openai_api_key_here" in env_content:
                print("⚠️  OpenAI API key not configured in backend/.env")
                return False
            else:
                print("✅ OpenAI API key appears to be configured")
    except Exception as e:
        print(f"❌ Error reading backend .env: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("Voice Buddy Setup Test")
    print("=" * 40)
    
    # Check environment
    print("\n1. Checking environment configuration...")
    env_ok = check_environment()
    
    # Test backend
    print("\n2. Testing backend...")
    backend_ok = test_backend_health()
    
    # Test frontend
    print("\n3. Testing frontend...")
    frontend_ok = test_frontend()
    
    # Test API endpoints
    if backend_ok:
        print("\n4. Testing API endpoints...")
        api_ok = test_api_endpoints()
    else:
        print("\n4. Skipping API tests (backend not running)")
        api_ok = False
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY")
    print("=" * 40)
    
    if env_ok and backend_ok and frontend_ok and api_ok:
        print("🎉 All tests passed! Your Voice Buddy setup is working correctly.")
        print("\nYou can now:")
        print("- Open http://localhost:3000 to use the chat interface")
        print("- View API docs at http://localhost:8000/docs")
    else:
        print("❌ Some tests failed. Please check the issues above.")
        print("\nTroubleshooting:")
        if not env_ok:
            print("- Configure your .env files with proper values")
        if not backend_ok:
            print("- Make sure backend is running: cd backend && python main.py")
        if not frontend_ok:
            print("- Make sure frontend is running: cd frontend && npm run dev")
        if not api_ok:
            print("- Check backend logs for API errors")

if __name__ == "__main__":
    main()
