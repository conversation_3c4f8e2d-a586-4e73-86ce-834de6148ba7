'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Send, Mic, MicOff, Volume2, VolumeX, Settings, MessageSquare } from 'lucide-react';
import { chatAPI, voiceAPI, ChatMessage, Conversation } from '@/services/api';
import { useVoiceRecording } from '@/hooks/useVoiceRecording';

interface ChatInterfaceProps {
  username: string;
  initialLanguage?: string;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({ 
  username, 
  initialLanguage = 'en' 
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [currentConversation, setCurrentConversation] = useState<number | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [language, setLanguage] = useState(initialLanguage);
  const [responseType, setResponseType] = useState<'text' | 'voice' | 'both'>('text');
  const [isLoading, setIsLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  
  const { 
    isRecording, 
    startRecording, 
    stopRecording, 
    error: recordingError 
  } = useVoiceRecording();

  const supportedLanguages = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Spanish' },
    { code: 'fr', name: 'French' },
    { code: 'de', name: 'German' },
    { code: 'it', name: 'Italian' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'ru', name: 'Russian' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'zh', name: 'Chinese' },
    { code: 'ar', name: 'Arabic' },
    { code: 'hi', name: 'Hindi' },
  ];

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    loadConversations();
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadConversations = async () => {
    try {
      // For demo purposes, we'll use user ID 1
      // In a real app, you'd get this from authentication
      const convs = await chatAPI.getConversations(1);
      setConversations(convs);
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  };

  const loadMessages = async (conversationId: number) => {
    try {
      const msgs = await chatAPI.getMessages(conversationId);
      setMessages(msgs);
      setCurrentConversation(conversationId);
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const sendTextMessage = async () => {
    if (!inputText.trim()) return;

    setIsLoading(true);
    try {
      const response = await chatAPI.sendMessage({
        message: inputText,
        conversation_id: currentConversation || undefined,
        username,
        language,
        response_type: responseType
      });

      // Add user message
      const userMessage: ChatMessage = {
        id: Date.now(),
        content: inputText,
        is_user_message: true,
        message_type: 'text',
        created_at: new Date().toISOString()
      };

      // Add AI response
      const aiMessage: ChatMessage = {
        id: Date.now() + 1,
        content: response.response_text,
        is_user_message: false,
        message_type: responseType,
        voice_file_path: response.voice_file_url,
        created_at: new Date().toISOString()
      };

      setMessages(prev => [...prev, userMessage, aiMessage]);
      setCurrentConversation(response.conversation_id);
      setInputText('');

      // Play voice response if available
      if (response.voice_file_url && (responseType === 'voice' || responseType === 'both')) {
        playAudio(response.voice_file_url);
      }

      // Reload conversations to update the list
      loadConversations();

    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sendVoiceMessage = async () => {
    if (isRecording) {
      const audioBlob = await stopRecording();
      if (audioBlob) {
        setIsLoading(true);
        try {
          const audioFile = new File([audioBlob], 'recording.webm', { 
            type: 'audio/webm' 
          });

          const response = await voiceAPI.voiceChat(
            audioFile,
            username,
            currentConversation || undefined,
            language,
            responseType
          );

          // Add user message
          const userMessage: ChatMessage = {
            id: Date.now(),
            content: response.user_text,
            is_user_message: true,
            message_type: 'voice',
            created_at: new Date().toISOString()
          };

          // Add AI response
          const aiMessage: ChatMessage = {
            id: Date.now() + 1,
            content: response.ai_response,
            is_user_message: false,
            message_type: responseType,
            voice_file_path: response.voice_file_url,
            created_at: new Date().toISOString()
          };

          setMessages(prev => [...prev, userMessage, aiMessage]);
          setCurrentConversation(response.conversation_id);

          // Play voice response if available
          if (response.voice_file_url && (responseType === 'voice' || responseType === 'both')) {
            playAudio(response.voice_file_url);
          }

          loadConversations();

        } catch (error) {
          console.error('Error sending voice message:', error);
        } finally {
          setIsLoading(false);
        }
      }
    } else {
      startRecording();
    }
  };

  const playAudio = (audioUrl: string) => {
    if (audioRef.current) {
      audioRef.current.src = `http://localhost:8000${audioUrl}`;
      audioRef.current.play().catch(console.error);
    }
  };

  const startNewConversation = async () => {
    try {
      await chatAPI.createConversation(username, language);
      setMessages([]);
      setCurrentConversation(null);
      loadConversations();
    } catch (error) {
      console.error('Error creating conversation:', error);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Voice Buddy</h2>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <Settings size={20} />
            </button>
          </div>
          
          <button
            onClick={startNewConversation}
            className="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center gap-2"
          >
            <MessageSquare size={16} />
            New Chat
          </button>
        </div>

        {/* Settings Panel */}
        {showSettings && (
          <div className="p-4 border-b border-gray-200 bg-gray-50">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Language</label>
                <select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                >
                  {supportedLanguages.map(lang => (
                    <option key={lang.code} value={lang.code}>
                      {lang.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Response Type</label>
                <select
                  value={responseType}
                  onChange={(e) => setResponseType(e.target.value as 'text' | 'voice' | 'both')}
                  className="w-full p-2 border border-gray-300 rounded-lg"
                >
                  <option value="text">Text Only</option>
                  <option value="voice">Voice Only</option>
                  <option value="both">Text + Voice</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Conversations List */}
        <div className="flex-1 overflow-y-auto">
          {conversations.map(conv => (
            <div
              key={conv.id}
              onClick={() => loadMessages(conv.id)}
              className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                currentConversation === conv.id ? 'bg-blue-50 border-blue-200' : ''
              }`}
            >
              <div className="font-medium truncate">
                {conv.title || 'New Conversation'}
              </div>
              <div className="text-sm text-gray-500">
                {new Date(conv.created_at).toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map(message => (
            <div
              key={message.id}
              className={`flex ${message.is_user_message ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.is_user_message
                    ? 'bg-blue-500 text-white'
                    : 'bg-white border border-gray-200'
                }`}
              >
                <div className="text-sm">{message.content}</div>
                {message.voice_file_path && (
                  <button
                    onClick={() => playAudio(message.voice_file_path!)}
                    className="mt-2 p-1 hover:bg-gray-100 rounded"
                  >
                    <Volume2 size={16} />
                  </button>
                )}
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-white border border-gray-200 px-4 py-2 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                  <span className="text-sm">AI is thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="border-t border-gray-200 p-4">
          {recordingError && (
            <div className="mb-2 text-red-500 text-sm">{recordingError}</div>
          )}
          
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendTextMessage()}
              placeholder="Type your message..."
              className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
            
            <button
              onClick={sendTextMessage}
              disabled={isLoading || !inputText.trim()}
              className="p-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send size={20} />
            </button>
            
            <button
              onClick={sendVoiceMessage}
              disabled={isLoading}
              className={`p-3 rounded-lg ${
                isRecording 
                  ? 'bg-red-500 text-white animate-pulse' 
                  : 'bg-green-500 text-white hover:bg-green-600'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isRecording ? <MicOff size={20} /> : <Mic size={20} />}
            </button>
          </div>
        </div>
      </div>

      {/* Hidden audio element for playing responses */}
      <audio ref={audioRef} />
    </div>
  );
};
