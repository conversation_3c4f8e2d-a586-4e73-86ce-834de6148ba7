from pydantic import BaseSettings
from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://username:password@localhost/voice_buddy")
    
    # OpenAI
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Voice Buddy AI Chat Bot"
    
    # CORS
    BACKEND_CORS_ORIGINS: list = ["http://localhost:3000", "http://127.0.0.1:3000"]
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # File Upload
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    
    # Voice Settings
    DEFAULT_VOICE_LANGUAGE: str = "en"
    SUPPORTED_LANGUAGES: list = [
        "en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh", 
        "ar", "hi", "tr", "pl", "nl", "sv", "da", "no", "fi"
    ]
    
    class Config:
        case_sensitive = True

settings = Settings()
